import os
import numpy as np
import SimpleITK as sitk
import pyvista as pv
import imageio.v2 as iio

# ======================
# 读取 DICOM → 构建体数据
# ======================
def load_dicom_series(series_dir: str):
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(series_dir)
    if not series_ids:
        raise RuntimeError(f"No DICOM series in: {series_dir}")

    file_names = reader.GetGDCMSeriesFileNames(series_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()                        # SimpleITK Image

    vol_zyx = sitk.GetArrayFromImage(img).astype(np.float32)  # (z,y,x), TOF: 血管亮
    spacing = img.GetSpacing()                    # (sx, sy, sz)
    origin  = img.GetO<PERSON>in()                     # (ox, oy, oz)

    # ------- TOF血管优化预处理 -------
    print(f"原始强度范围: [{vol_zyx.min():.1f}, {vol_zyx.max():.1f}]")

    # 1. 基础预处理：更宽松的百分位裁剪，保留更多细节
    p0_5, p99_5 = np.percentile(vol_zyx, (0.5, 99.5))
    vol_zyx = np.clip(vol_zyx, p0_5, p99_5)
    vol_zyx = (vol_zyx - p0_5) / (p99_5 - p0_5 + 1e-6)

    # 2. 双阶段增强：先整体增强，再血管特异性增强
    # 阶段1：温和的对比度增强
    vol_zyx = np.power(vol_zyx, 0.7)  # gamma < 1 增强亮区域

    # 阶段2：血管特异性增强
    # 识别潜在血管区域（高强度区域）
    vessel_threshold = np.percentile(vol_zyx, 85)  # 85%作为血管阈值
    vessel_mask = vol_zyx > vessel_threshold

    # 对血管区域进行适度增强
    vol_enhanced = vol_zyx.copy()
    vol_enhanced[vessel_mask] = vol_enhanced[vessel_mask] * 1.2  # 轻微增强血管

    # 对非血管区域进行轻微抑制，但保留背景结构
    background_mask = vol_zyx < np.percentile(vol_zyx, 50)
    vol_enhanced[background_mask] = vol_enhanced[background_mask] * 0.8

    vol_zyx = vol_enhanced
    print(f"增强后强度范围: [{vol_zyx.min():.3f}, {vol_zyx.max():.3f}]")
    print(f"血管区域比例: {vessel_mask.sum() / vessel_mask.size:.3%}")

    # VTK 使用 (x,y,z) & Fortran 顺序
    vol_xyz = np.ascontiguousarray(np.transpose(vol_zyx, (2, 1, 0)))  # (x,y,z)
    nx, ny, nz = vol_xyz.shape

    grid = pv.ImageData()
    grid.dimensions = (nx, ny, nz)
    grid.spacing    = spacing
    grid.origin     = origin
    grid.point_data["scalars"] = vol_xyz.ravel(order="F")
    return grid, vol_zyx  # grid 用于 GPU 渲染，vol_zyx 用于快速 MIP PNG

# ======================
# 静态 MIP PNG 导出（CPU 快速）
# ======================
def save_mip_pngs(vol_zyx: np.ndarray, out_prefix="tof_mip"):
    """
    生成三个正交方向的最大强度投影(MIP)
    vol_zyx: (z,y,x) 体数据
    """
    # AP（沿 z 轴最大投影）- 前后位
    mip_ap = vol_zyx.max(axis=0)  # (y,x)
    iio.imwrite(f"{out_prefix}_AP.png", (mip_ap * 255).astype(np.uint8))

    # LAT（沿 x 轴最大投影）- 侧位
    mip_lat = vol_zyx.max(axis=2)  # (z,y)
    iio.imwrite(f"{out_prefix}_LAT.png", (mip_lat * 255).astype(np.uint8))

    # SI（沿 y 轴最大投影）- 上下位
    mip_si = vol_zyx.max(axis=1)  # (z,x)
    iio.imwrite(f"{out_prefix}_SI.png", (mip_si * 255).astype(np.uint8))

    print(f"Saved: {out_prefix}_AP.png, {out_prefix}_LAT.png, {out_prefix}_SI.png")

    # 输出MIP统计信息用于验证
    print(f"MIP AP shape: {mip_ap.shape}, range: [{mip_ap.min():.3f}, {mip_ap.max():.3f}]")
    print(f"MIP LAT shape: {mip_lat.shape}, range: [{mip_lat.min():.3f}, {mip_lat.max():.3f}]")
    print(f"MIP SI shape: {mip_si.shape}, range: [{mip_si.min():.3f}, {mip_si.max():.3f}]")

# ======================
# GPU MIP 旋转视频（PyVista/VTK）
# ======================
def make_mip_spin_video(
    grid: pv.ImageData,
    out_path: str = "tof3d_mip_spin.mp4",
    seconds: int = 10,
    fps: int = 30,
    window_size=(1280, 960),
    codec: str = "h264_videotoolbox",  # macOS 硬编；Linux 可改 h264_nvenc 或 libx264
    quality: int = 7                   # 0~10，越高码率越大
):
    n_frames = seconds * fps
    az_step = 360.0 / n_frames

    pv.global_theme.smooth_shading = False  # MIP 不需要光照
    pl = pv.Plotter(off_screen=True, window_size=window_size)
    pl.set_background("black")

    # 添加体并启用 MIP（Maximum Intensity Projection）
    vol = pl.add_volume(
        grid,
        cmap="gray",
        shade=False,                 # 关闭着色，纯强度投影
        opacity="linear",            # MIP 实际由 blend 模式决定，透明度影响较小
    )
    # 关键：设置 VTK 映射器为“最大强度投影”
    # 等价 VTK：mapper.SetBlendModeToMaximumIntensity()
    vol.mapper.SetBlendModeToMaximumIntensity()

    # 优化MIP渲染质量
    vol.mapper.SetSampleDistance(0.5)  # 减小采样距离，提高质量
    vol.mapper.SetAutoAdjustSampleDistances(False)  # 禁用自动调整

    # 调整初始视角（可按需改成 pl.view_xz()/pl.view_yz() 等）
    pl.view_isometric()

    # 打开视频写入
    pl.open_movie(out_path, framerate=fps, quality=quality, codec=codec)

    # 渲一帧初始化
    pl.show(auto_close=False)
    pl.render()
    pl.write_frame()

    # 环绕一周
    for _ in range(n_frames - 1):
        pl.camera.azimuth += az_step      # 水平环绕
        # pl.camera.elevation(0.2)      # 如需轻微俯仰，可打开
        pl.render()
        pl.write_frame()

    pl.close()
    print(f"Saved: {os.path.abspath(out_path)}")

# ======================
# 主函数
# ======================
if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"

    grid, vol_zyx = load_dicom_series(DICOM_DIR)
    save_mip_pngs(vol_zyx, out_prefix="tof_mip")

    make_mip_spin_video(
        grid,
        out_path="tof3d_mip_spin.mp4",
        seconds=12,
        fps=30,
        codec="h264_videotoolbox",   # macOS 硬编；Linux 可改 h264_nvenc/libx264
        window_size=(1280, 960)
    )
