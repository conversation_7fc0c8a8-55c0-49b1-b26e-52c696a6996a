#!/usr/bin/env python3
"""
预览当前MIP结果
"""
import matplotlib.pyplot as plt
import imageio.v2 as iio
import numpy as np

def preview_current_mip():
    """预览当前生成的MIP结果"""
    
    try:
        # 加载当前结果
        mip_ap = iio.imread("tof_mip_AP.png")
        mip_lat = iio.imread("tof_mip_LAT.png") 
        mip_si = iio.imread("tof_mip_SI.png")
        
        # 创建预览图
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        axes[0].imshow(mip_ap, cmap='gray')
        axes[0].set_title('MIP AP (Anterior-Posterior)\n血管增强版本', fontsize=12)
        axes[0].axis('off')
        
        axes[1].imshow(mip_lat, cmap='gray')
        axes[1].set_title('MIP LAT (Lateral)\n血管增强版本', fontsize=12)
        axes[1].axis('off')
        
        axes[2].imshow(mip_si, cmap='gray')
        axes[2].set_title('MIP SI (Superior-Inferior)\n血管增强版本', fontsize=12)
        axes[2].axis('off')
        
        plt.suptitle('当前TOF MIP结果 - 血管优化增强', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('current_mip_preview.png', dpi=150, bbox_inches='tight')
        print("✅ 保存当前结果预览: current_mip_preview.png")
        
        # 分析图像质量
        print("\n=== 当前MIP质量分析 ===")
        
        def analyze_image(img, name):
            """分析图像质量指标"""
            img_gray = img if len(img.shape) == 2 else np.mean(img, axis=2)
            
            # 对比度
            contrast = np.std(img_gray)
            
            # 动态范围
            dynamic_range = img_gray.max() - img_gray.min()
            
            # 血管区域分析（高强度区域）
            high_intensity_threshold = 200  # 对于0-255范围
            vessel_pixels = (img_gray > high_intensity_threshold).sum()
            vessel_ratio = vessel_pixels / img_gray.size
            
            # 背景区域分析（中等强度区域）
            mid_intensity_mask = (img_gray > 50) & (img_gray <= 200)
            background_ratio = mid_intensity_mask.sum() / img_gray.size
            
            print(f"{name}:")
            print(f"  图像尺寸: {img_gray.shape}")
            print(f"  强度范围: [{img_gray.min():.0f}, {img_gray.max():.0f}]")
            print(f"  对比度: {contrast:.2f}")
            print(f"  动态范围: {dynamic_range:.0f}")
            print(f"  血管区域比例: {vessel_ratio:.3%}")
            print(f"  背景区域比例: {background_ratio:.3%}")
            
            return contrast, dynamic_range, vessel_ratio, background_ratio
        
        print("\n--- 各视图分析 ---")
        ap_stats = analyze_image(mip_ap, "AP视图")
        lat_stats = analyze_image(mip_lat, "LAT视图")
        si_stats = analyze_image(mip_si, "SI视图")
        
        # 评估结果
        print(f"\n📊 整体评估:")
        avg_contrast = (ap_stats[0] + lat_stats[0] + si_stats[0]) / 3
        avg_vessel_ratio = (ap_stats[2] + lat_stats[2] + si_stats[2]) / 3
        
        print(f"平均对比度: {avg_contrast:.2f}")
        print(f"平均血管区域比例: {avg_vessel_ratio:.3%}")
        
        if avg_contrast > 40:
            print("✅ 对比度良好，血管清晰可见")
        elif avg_contrast > 25:
            print("⚠️  对比度适中，可能需要进一步优化")
        else:
            print("❌ 对比度偏低，需要增强")
            
        if 0.01 < avg_vessel_ratio < 0.05:
            print("✅ 血管区域比例合理")
        elif avg_vessel_ratio > 0.05:
            print("⚠️  血管区域比例偏高，可能包含过多背景")
        else:
            print("⚠️  血管区域比例偏低，可能增强不足")
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ 无法找到MIP文件: {e}")
        return False

def create_algorithm_summary():
    """创建当前算法总结"""
    print("\n" + "="*60)
    print("当前TOF血管优化MIP算法")
    print("="*60)
    
    print("\n🎯 目标: 达到理想的血管显示效果")
    print("- 血管清晰可见，包括主要血管和分支")
    print("- 背景组织适度抑制但仍可见")
    print("- 对比度适中，不过度增强")
    
    print("\n⚙️ 算法步骤:")
    print("1. 宽松百分位裁剪: 0.5% - 99.5% (保留更多细节)")
    print("2. 温和gamma增强: γ=0.7 (增强亮区域)")
    print("3. 血管特异性增强:")
    print("   - 识别血管区域: >85%百分位")
    print("   - 血管区域增强: ×1.2")
    print("   - 背景区域抑制: ×0.8")
    
    print("\n📈 预期改进:")
    print("- 血管区域比例: ~15% (适中)")
    print("- 保持背景结构可见性")
    print("- 避免过度处理导致的失真")
    
    print("\n📁 生成文件:")
    print("  ✓ tof_mip_AP.png - 前后位MIP")
    print("  ✓ tof_mip_LAT.png - 侧位MIP")
    print("  ✓ tof_mip_SI.png - 上下位MIP")
    print("  ✓ tof3d_mip_spin.mp4 - 3D旋转视频")
    print("  ✓ current_mip_preview.png - 结果预览")

if __name__ == "__main__":
    print("预览当前TOF MIP结果...")
    
    success = preview_current_mip()
    
    if success:
        create_algorithm_summary()
        print(f"\n🎉 当前版本生成完成！")
        print("请查看 current_mip_preview.png 来验证是否达到了理想的血管显示效果。")
        print("如果需要进一步调整，请告诉我具体需要改进的地方。")
    else:
        print("❌ 预览生成失败")
